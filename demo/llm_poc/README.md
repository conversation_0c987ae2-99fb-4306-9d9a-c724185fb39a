# services/llm - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：  
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 目标与背景（与蓝图一致）

LLM Integration 服务负责以安全、可控、可切换的方式调用大语言模型（远端 API 或本地模型），并为 Conversation Service 提供生成、评分与日志记录接口。设计遵循 PROJECT_BLUEPRINT 的“异步优先、可插拔、低维护成本”原则。

---
## 功能范围（POC）

- 提供标准化、异步的 generate 接口（POST /api/v1/llm/generate）
- 支持多后端：OpenAI（最新GPT-5已发布，GPT-4o-mini仍为成本效益选择）
- Prompt 管理：模板拼接、短期/长期记忆插入（长期记忆由 Manticore 异步检索提供）
- 费用与速率控制：在策略层支持模型路由与限速
- 日志与追踪：与 Sentry 集成用于错误/性能监控

---
## 技术细节（按蓝图）

- 框架：FastAPI（async）
- Prompt 与类型：Pydantic 2.5+ 用于请求/响应 schema
- 模型适配层：抽象 LLMProvider 接口（openai）
- 异步 IO：httpx.AsyncClient 调用远端 API（推荐，性能优秀）
- 配置项（示例 .env）：
  - LLM_PROVIDER=openai
  - OPENAI_API_KEY=xxxxx
  - OPENAI_MODEL=gpt-4o-mini  # 成本效益选择，或gpt-5（最新，2025年8月发布）
  - LLM_MAX_TOKENS=4096  # GPT-4o-mini支持更大上下文
  - LLM_TEMPERATURE=0.2
- 长期记忆接入：调用 Manticore 异步客户端检索锚点并拼接 Prompt

---
## 最小化 demo（POC）

- demo/llm_poc/
  - README.md（本文件）
  - requirements.txt
  - main.py / test_poc.py（演示：构建 prompt -> 调用 LLM -> 返回结果）

示例 requirements.txt（示意）:
```
fastapi
uvicorn[standard]
pydantic>=2.5
httpx  # 推荐：异步HTTP客户端，性能优秀
openai>=1.0.0  # 最新版本，支持GPT-4o系列
git+https://github.com/manticoresoftware/manticoresearch-python-asyncio.git  # 异步搜索客户端
```

---
## POC 流程（伪代码）

1. Conversation Service 请求 LLM：/api/v1/llm/generate { conversation_id, user_query }
2. LLM Service 拉取长期记忆：调用 Manticore 检索相关锚点
3. 使用 Prompt 模板拼接（系统提示、长期记忆、短期消息、用户问题）
4. 异步调用后端模型（OpenAI）
5. 返回生成文本并记录用量与 metadata

伪代码示例：
```python
from fastapi import FastAPI
from llm.providers import OpenAIProvider

app = FastAPI()
provider = OpenAIProvider(api_key=os.getenv("OPENAI_API_KEY"))

@app.post("/api/v1/llm/generate")
async def generate(req: GenerateRequest):
    context = await fetch_long_term_memory(req.conversation_id)  # Manticore
    prompt = build_prompt(system_prompt, context, req.history, req.query)
    resp = await provider.generate(prompt, max_tokens=CONFIG.LLM_MAX_TOKENS)
    return {"text": resp.text, "usage": resp.usage}
```

---
## 集成点（与蓝图对齐）

- 上游：Conversation Service, Topic Service（提供会话历史与上下文）
- 外部/下游：Embedding Service（如需向量检索增强）、Manticore（长期记忆检索）
- 异步任务：对长任务或成本高的生成，使用 Dramatiq 进行异步处理与降峰

---
## 验证清单（与蓝图 P1 对齐）

- [ ] 能用 Manticore 提供的长期记忆片段拼接 Prompt 并得到合理生成
- [ ] 能在 OpenAI 模型间切换（配置驱动）
- [ ] 生成结果含有必要的 metadata（token 使用、延迟）
- [ ] Prompt 构建逻辑保证不超 token 限制（当超出时进行摘要或截断）

---
## 风险与注意事项

- Prompt 注入安全：限制可插入的记忆长度并对用户输入进行清洗
- 成本控制：记录并限制远端 API 调用（按会话或每用户计）
- 延迟与并发控制：对高并发场景使用限流与降级策略

---
## 参考（项目内）

- docs/PROJECT_BLUEPRINT.md（总体技术栈）
- backend/app/api/（FastAPI 路由与 deps 模式参考）