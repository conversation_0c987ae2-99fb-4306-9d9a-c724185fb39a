{"generated_at": "2025-08-14T12:07:39.000Z", "summary": "Aggregated top-level directory scan (sampled, max 20 entries per directory). Use scripts/generate_shrimp_scan.py to regenerate full scan.", "directories": {"root": [".env", ".env.example", "docker-compose.yml", "docker-compose.override.yml", "copier.yml", "README.md", "pyproject.toml", ".giti<PERSON>re", ".gitattributes"], "backend": ["backend/app/main.py", "backend/pyproject.toml", "backend/Dockerfile", "backend/alembic.ini", "backend/test_all_features.py", "backend/app/services/document_parser.py", "backend/app/tests/test_documents_integration.py"], "demo": ["demo/.env", "demo/.env.template", "demo/DEMO_DEV_ORDER.md", "demo/document_poc/README.md", "demo/document_poc/main.py", "demo/embedding_poc/main.py", "demo/manticore_poc/README.md", "demo/manticore_poc/async_wrapper_test.py", "demo/test_text_splitter.py"], "docs": ["docs/2_Architecture/04_implementation_guide.md", "docs/1_Product/01_brief.md", "docs/1_Product/02_prd_mvp.md", "docs/2_Architecture/02_data_model_v1.md"], "engines": ["engines/text_splitter/", "engines/search/"], "frontend": ["frontend/src/", "frontend/public/", "frontend/package.json"], "services": ["services/document/", "services/embedding/", "services/llm/"], "deployment": ["deployment/docker/", "deployment/k8s/", "deployment/scripts/"], "scripts": ["scripts/", "backend/scripts/test.sh", "backend/scripts/format.sh", "backend/scripts/lint.sh"]}, "notes": {"max_per_directory": 20, "regenerate_command": "python scripts/generate_shrimp_scan.py --output demo/shrimp_scan_summary.json", "next_step": "Use demo/shrimp_scan_summary.json as input to generate shrimp-rules.md"}}