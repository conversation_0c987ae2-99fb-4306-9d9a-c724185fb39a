# Development Guidelines

## 项目概览
- 使用此文件作为 AI Agent 的操作规范；仅包含项目特定规则与文件联动要求，禁止包含通用开发知识。
- 参照扫描输出：[`demo/shrimp_scan_summary.json`](demo/shrimp_scan_summary.json:1) 作为关键路径索引来源。

## 目录与关键文件映射（必须引用）
- 必须始终参考并验证下列路径（至少检查存在性与修改影响）：
  - 根目录：[`/.env`](.env:1)、[`/.env.example`](.env.example:1)、[`/docker-compose.yml`](docker-compose.yml:1)、[`/pyproject.toml`](pyproject.toml:1)
  - 后端：[`/backend/app/main.py`](backend/app/main.py:1)、[`/backend/pyproject.toml`](backend/pyproject.toml:1)、[`/backend/Dockerfile`](backend/Dockerfile:1)
  - demo：[`/demo/.env.template`](demo/.env.template:1)、[`/demo/DEMO_DEV_ORDER.md`](demo/DEMO_DEV_ORDER.md:1)、[`/demo/document_poc/README.md`](demo/document_poc/README.md:1)
  - 文档：[`/docs/2_Architecture/04_implementation_guide.md`](docs/2_Architecture/04_implementation_guide.md:1)
  - 引擎：[`/engines/text_splitter/`](engines/text_splitter/:1)
- 若规则中引用新文件或脚本，使用相对路径并在文档顶部列出索引，索引用于变更影响评估。

## 操作与修改通则（命令式）
- 在对任何源文件进行修改前，强制执行：检查受影响文件清单 -> 备份受影响文件 -> 在工作分支上提交变更草案。
- 在修改与配置相关的文件（如 `.env`、`docker-compose.yml`、`backend/Dockerfile`）时，标记为“高风险变更”，并触发审批流程（见“审批模板”）。
- 新增或删除顶层目录文件（如删除 `demo/*`、`docs/*`）前，禁止直接提交，必须通过审批并在审计日志记录变更摘要。
- 所有文档更新必须包含变更摘要行，格式：DATE | PATHS_CHANGED | BRIEF_REASON（写入变更审计日志文件：[`demo/shrimp_rules_change_log.json`](demo/shrimp_rules_change_log.json:1)）。

## 关键文件交互标准（按目录聚合）
- 修改 [`demo/document_poc/README.md`](demo/document_poc/README.md:1) 时，必须同时更新：
  - [`docs/2_Architecture/04_implementation_guide.md`](docs/2_Architecture/04_implementation_guide.md:1)
  - [`demo/DEMO_DEV_ORDER.md`](demo/DEMO_DEV_ORDER.md:1)
- 修改根级 `.env` 模板（例如 [`/demo/.env.template`](demo/.env.template:1) 或 [`/.env.example`](.env.example:1)）时，必须同时检查并同步注释到：
  - [`demo/embedding_poc/.env`](demo/embedding_poc/.env:1)（若存在）
  - CI/CD 配置（若仓库存在相关文件）
- 修改搜索/索引相关实现（如 `/engines/text_splitter/` 或 `/manticore/`）时，必须同时更新 demo 示例与测试脚本：
  - [`demo/test_text_splitter.py`](demo/test_text_splitter.py:1)
  - 任何 demo 中依赖该引擎的示例文件
- 修改 API 接口入口（如 [`/backend/app/api/main.py`](backend/app/api/main.py:1)）时，必须同时更新测试文件夹中对应的测试用例路径（例如：`backend/app/tests/` 下的相关测试）。

## 功能实现标准（命令式步骤）
- 添加新功能时执行顺序：
  1. 在本地工作分支创建变更。
  2. 更新或新增实现文件。
  3. 更新受影响的示例/文档（遵循“关键文件交互标准”）。
  4. 运行相关测试脚本（若无测试，则至少运行 demo 示例脚本）。
  5. 写入审计日志：[`demo/shrimp_rules_change_log.json`](demo/shrimp_rules_change_log.json:1)。
  6. 若高风险，触发审批流程并等待批准。
- 小幅文档或示例修正（仅文案更正且不影响行为）可直接提交，但仍需在审计日志中记录。

## 第三方依赖变更标准
- 修改依赖管理文件（如 [`/pyproject.toml`](pyproject.toml:1)、[`/backend/pyproject.toml`](backend/pyproject.toml:1) 或 `requirements.txt`）时：
  - 列出新增/移除依赖及其用途。
  - 评估兼容性影响并记录在审计日志。
  - 若影响运行时部署或基础镜像，标记为高风险并触发审批。

## 风险分级与审批触发（命令式决策树）
- 定义：
  - 低风险：仅文档或 demo 示例的小改动，不影响运行或配置。
  - 中风险：修改非生产配置、测试脚本、或新增无外部依赖的轻量功能。
  - 高风险：修改生产配置（`.env`、Dockerfile、compose 文件）、删除文件、变更依赖或影响部署。
- 规则：
  - 如果判定为 高风险 -> 必须使用寸止审批模板（见下）并记录审计条目 -> 禁止进一步提交直到批准。
  - 如果判定为 中风险 -> 记录变更并通知（可继续提交）。
  - 如果判定为 低风险 -> 记录变更并自动提交。
- 审批模板位置：在文档中直接使用下列预定义消息供自动化填充并通过寸止发送：
  - 模板 A（高风险）："请求批准：对 PATHS 做出如下高风险更改：REASON。选项：[批准, 拒绝, 请求更多信息]"
  - 模板 B（中风险）："通知：将对 PATHS 做出中风险更改：REASON。选项：[记录并继续, 请求审批]"

## 寸止（zhi___）审批模板（复制并在需要时发送）
- 发送内容示例（必须使用寸止接口发送，不得用其他方式直接询问用户）：
  - 标题：审批请求 — 高风险变更
  - 正文必须包含：变更摘要、受影响路径清单、回滚步骤（简要）、变更执行者
  - 预定义选项（必须包含在消息中）：["批准","拒绝","请求更多信息"]
- 在文档中引用示例：使用模板并填入实际 PATHS，例如：`["批准","拒绝","请求更多信息"]`。

## 模糊请求自治处理（命令式流程）
- 收到模糊或不完整指令（例如“更新规则”）时，执行下列步骤：
  1. 自动运行扫描脚本：[`scripts/generate_shrimp_scan.py`](scripts/generate_shrimp_scan.py:1)（若不存在则先创建并记录）。
  2. 对比现有 [`shrimp-rules.md`](shrimp-rules.md:1)（若存在）并生成差异草案。
  3. 将草案分类为 低/中/高 风险项并自动应用低风险变更（写入审计日志）。
  4. 对中/高 风险变更生成寸止审批消息并等待批准。
  5. 在审计日志中记录所有自动化决策与理由。
- 严禁在未执行以上流程前对高风险文件进行直接修改。

## 审计与变更记录（命令式）
- 所有变更必须追加到变更日志：[`demo/shrimp_rules_change_log.json`](demo/shrimp_rules_change_log.json:1)。
- 变更记录条目字段（必须包含）：
  - timestamp（ISO 8601）
  - actor（执行者标识）
  - paths_changed（数组）
  - risk_level（low|medium|high）
  - summary（简短一句话）
  - approval_state（pending|approved|rejected）
- 若审批通过，写入批准者与批准时间。

## 示例：可做 / 不可做（命令式，至少 5 条）
- 示例 1（文档更新）：
  - 可做：修改 [`demo/document_poc/README.md`](demo/document_poc/README.md:1) 的示例命令并同时在审计日志中记录。
  - 不可做：修改该 README 后不更新 [`docs/2_Architecture/04_implementation_guide.md`](docs/2_Architecture/04_implementation_guide.md:1)。
- 示例 2（env 模板）：
  - 可做：在 [`demo/.env.template`](demo/.env.template:1) 增加注释说明并同时同步到根级 `.env.example`。
  - 不可做：直接修改生产 `.env`（根目录）而不触发审批。
- 示例 3（依赖变更）：
  - 可做：在 `pyproject.toml` 添加 dev-only 工具，并记录影响。
  - 不可做：移除或升级核心运行时依赖而不触发审批。
- 示例 4（演示脚本）：
  - 可做：修复 [`demo/test_text_splitter.py`](demo/test_text_splitter.py:1) 中的示例路径并记录。
  - 不可做：删除 demo 中的关键示例文件而不记录或通知。
- 示例 5（部署配置）：
  - 可做：在 `deployment/` 下增加说明性文档并同步到 `docs/`。
  - 不可做：修改 `deployment/docker/` 配置影响镜像运行而不触发审批。

## 变更回滚指导（命令式）
- 高风险变更必须包含回滚步骤并在审计日志中列出回滚命令或路径。
- 在批准前，不允许合并任何高风险变更到主分支。

## 扫描脚本与自动化（命令式）
- 推荐扫描脚本位置：[`scripts/generate_shrimp_scan.py`](scripts/generate_shrimp_scan.py:1)。
- 生成或更新扫描摘要的示例命令：`python scripts/generate_shrimp_scan.py --output demo/shrimp_scan_summary.json`
- 若需完整扫描结果，请使用脚本并将输出路径指定为 `demo/shrimp_scan_summary.json`。

## 限制与禁止项（绝对命令）
- 禁止在此文件中包含任何通用开发知识、教程性说明或与项目无关的通用最佳实践描述。
- 禁止在未记录审计日志的情况下执行中/高 风险变更。
- 禁止绕过寸止审批流程对高风险文件直接提交。
- 禁止在规则中引用未存在的文件路径；所有引用路径必须在仓库中可验证（使用 [`demo/shrimp_scan_summary.json`](demo/shrimp_scan_summary.json:1) 验证）。

## 附录：快速操作清单（命令式）
- 生成扫描摘要：`python scripts/generate_shrimp_scan.py --output demo/shrimp_scan_summary.json`
- 生成或更新规则（草案）：
  - 读取：[`demo/shrimp_scan_summary.json`](demo/shrimp_scan_summary.json:1)
  - 写入：`/shrimp-rules.md`
  - 记录：追加到 [`demo/shrimp_rules_change_log.json`](demo/shrimp_rules_change_log.json:1)
- 触发审批（高风险）：
  - 使用寸止发送审批模板并包含预定义选项：["批准","拒绝","请求更多信息"]
