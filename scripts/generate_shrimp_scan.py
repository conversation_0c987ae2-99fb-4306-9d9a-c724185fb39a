#!/usr/bin/env python3
import os
import json
import argparse
from pathlib import Path

TOP_DIRS = [
    ".",
    "backend",
    "demo",
    "docs",
    "engines",
    "frontend",
    "services",
    "deployment",
    "scripts"
]

FILE_PATTERNS = [
    "README",
    ".env",
    ".env.template",
    "docker-compose",
    "pyproject.toml",
    "requirements.txt",
    "Dockerfile",
    "main.py",
    ".md"
]

MAX_PER_DIR = 20

def match_file(name):
    for p in FILE_PATTERNS:
        if p.startswith(".") and name == p:
            return True
        if p in name:
            return True
    return False

def scan_directory(root, top):
    found = []
    for dirpath, dirnames, filenames in os.walk(root):
        # limit depth to avoid huge scans for '.'
        rel = os.path.relpath(dirpath, start=top)
        for fn in filenames:
            if match_file(fn):
                path = os.path.join(dirpath, fn)
                found.append(os.path.normpath(path))
        # include directories that match (like engines/text_splitter)
        for d in list(dirnames):
            if match_file(d):
                path = os.path.join(dirpath, d)
                found.append(os.path.normpath(path))
        if len(found) >= MAX_PER_DIR:
            break
    return found[:MAX_PER_DIR]

def main():
    parser = argparse.ArgumentParser(description="Generate shrimp scan summary")
    parser.add_argument("--output", "-o", default="demo/shrimp_scan_summary.json")
    args = parser.parse_args()

    summary = {
        "generated_at": None,
        "summary": "Aggregated top-level directory scan (sampled, max {} entries per directory).".format(MAX_PER_DIR),
        "directories": {},
        "notes": {
            "max_per_directory": MAX_PER_DIR,
            "regenerate_command": "python scripts/generate_shrimp_scan.py --output demo/shrimp_scan_summary.json"
        }
    }

    from datetime import datetime
    summary["generated_at"] = datetime.utcnow().isoformat() + "Z"

    cwd = Path.cwd()

    for d in TOP_DIRS:
        root = cwd / d
        if not root.exists():
            continue
        entries = scan_directory(str(root), str(cwd))
        # normalize relative paths
        rels = [os.path.relpath(p, start=str(cwd)) for p in entries]
        summary["directories"][d] = rels

    out_path = Path(args.output)
    out_path.parent.mkdir(parents=True, exist_ok=True)
    with out_path.open("w", encoding="utf-8") as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print("Wrote scan summary to", str(out_path))

if __name__ == "__main__":
    main()