#!/usr/bin/env bash
# Collect git change outputs for shrimp task manager workflow
# Usage: bash scripts/collect_git_changes.sh

set -euo pipefail

echo "Collecting git status..."
git status --porcelain > changes_status.txt

echo "Collecting changed file names..."
git diff --name-only HEAD > changes_names.txt

echo "Collecting untracked files..."
git ls-files --others --exclude-standard > untracked.txt

echo "Done. Generated: changes_status.txt, changes_names.txt, untracked.txt"