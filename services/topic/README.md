# Topic Service

主题管理和文档关联服务，基于 PROJECT_BLUEPRINT.md 技术栈实现。

## ✅ 开发状态

**完成时间**: 2025-08-14  
**状态**: ✅ 开发完成并测试通过

## 🎯 功能特性

### 核心功能
- ✅ **主题 CRUD**: 创建、读取、更新、删除主题
- ✅ **文档关联**: 将文档关联到主题，支持多对多关系
- ✅ **Manticore 集成**: 检索主题相关的长期记忆锚点
- ✅ **用户隔离**: 基于用户ID的主题管理
- ✅ **统计功能**: 主题和用户的统计信息

### API 端点
```
POST   /api/v1/topics                    # 创建主题
GET    /api/v1/topics/{id}               # 获取主题详情
GET    /api/v1/topics                    # 获取主题列表
PUT    /api/v1/topics/{id}               # 更新主题
DELETE /api/v1/topics/{id}               # 删除主题

POST   /api/v1/topics/{id}/documents     # 关联文档
GET    /api/v1/topics/{id}/documents     # 获取关联文档
DELETE /api/v1/topics/{id}/documents/{doc_id}  # 取消关联

GET    /api/v1/topics/{id}/memory        # 获取记忆锚点
GET    /api/v1/topics/{id}/stats         # 获取主题统计
GET    /api/v1/users/{id}/topic-stats    # 获取用户统计

GET    /health                           # 健康检查
GET    /docs                             # API 文档
```

## 🏗️ 技术架构

### 技术栈
- **FastAPI**: Web 框架和 API 服务
- **SQLAlchemy**: ORM 和数据库操作
- **Pydantic**: 数据验证和序列化
- **PostgreSQL**: 主数据库（支持 SQLite 开发）
- **Redis**: 缓存和会话存储
- **Manticore Search**: 全文搜索和记忆检索
- **Uvicorn**: ASGI 服务器

### 项目结构
```
services/topic/
├── api/
│   ├── __init__.py
│   └── main.py              # FastAPI 应用和路由
├── models/
│   ├── __init__.py
│   └── topic.py             # 数据模型定义
├── services/
│   ├── __init__.py
│   ├── topic_service.py     # 主题业务逻辑
│   └── manticore_service.py # Manticore 集成
├── utils/
│   ├── __init__.py
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库工具
│   ├── exceptions.py        # 异常定义
│   └── health.py            # 健康检查
├── tests/
│   └── test_*.py            # 测试文件
├── __init__.py
├── requirements.txt         # 依赖包
├── run_service.py          # 服务启动脚本
├── simple_test.py          # 简单功能测试
└── README.md               # 本文件
```

## 🚀 快速开始

### 1. 安装依赖
```bash
cd services/topic
pip install -r requirements.txt
```

### 2. 配置环境
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

### 3. 启动服务
```bash
# 方式1: 使用启动脚本
python run_service.py

# 方式2: 直接使用 uvicorn
uvicorn api.main:app --host 0.0.0.0 --port 9004 --reload
```

### 4. 访问服务
- **API 文档**: http://localhost:9004/docs
- **健康检查**: http://localhost:9004/health
- **API 根路径**: http://localhost:9004/api/v1

## 🧪 测试

### 运行简单测试
```bash
python simple_test.py
```

### 测试结果示例
```
🎯 Topic Service 简单测试套件
==================================================
🔍 测试数据库连接...
✅ 数据库连接成功
🧪 开始 Topic Service 简单测试...
✅ 数据库初始化完成
✅ 创建主题成功: 测试主题 (ID: 1)
✅ 获取主题成功: 测试主题
✅ 文档关联: 成功
✅ 文档关联: 成功
✅ 主题文档: [123, 456]
✅ 主题文档数量: 2
✅ 用户主题数量: 1
✅ 用户主题总数: 4

🎉 Topic Service 简单测试完成！
```

## ⚙️ 配置说明

### 环境变量
```bash
# API 服务配置
TOPIC_API_HOST=0.0.0.0
TOPIC_API_PORT=9004
TOPIC_DEBUG=false

# 数据库配置
TOPIC_DATABASE_URL=postgresql://user:pass@localhost:5432/master_know

# Redis 配置
TOPIC_REDIS_URL=redis://localhost:6379/0

# Manticore 配置
TOPIC_MANTICORE_HOST=localhost
TOPIC_MANTICORE_PORT=9306

# 外部服务
TOPIC_USER_SERVICE_URL=http://localhost:9002
TOPIC_DOCUMENT_SERVICE_URL=http://localhost:9005

# 业务配置
TOPIC_MAX_TOPICS_PER_USER=100
TOPIC_MAX_DOCUMENTS_PER_TOPIC=50
```

## 🔗 集成说明

### 与其他服务的集成
- **用户服务**: 验证用户身份和权限
- **文档服务**: 获取文档元数据和内容
- **向量化服务**: 生成文档向量用于相似性搜索
- **Manticore Search**: 全文搜索和记忆锚点检索

### 数据库设计
```sql
-- 主题表
CREATE TABLE topics (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    user_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 主题文档关联表
CREATE TABLE topic_document_links (
    id SERIAL PRIMARY KEY,
    topic_id INTEGER REFERENCES topics(id),
    document_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(topic_id, document_id)
);

-- 主题统计表
CREATE TABLE topic_statistics (
    id SERIAL PRIMARY KEY,
    topic_id INTEGER REFERENCES topics(id),
    document_count INTEGER DEFAULT 0,
    conversation_count INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP,
    total_study_time INTEGER DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📊 监控和运维

### 健康检查
```bash
curl http://localhost:9004/health
```

### 日志监控
- 应用日志: 使用 Python logging
- 访问日志: Uvicorn 访问日志
- 错误日志: 异常堆栈跟踪

### 性能指标
- API 响应时间
- 数据库查询性能
- Manticore 搜索性能
- 缓存命中率

## 🔧 开发指南

### 添加新功能
1. 在 `models/topic.py` 中定义数据模型
2. 在 `services/topic_service.py` 中实现业务逻辑
3. 在 `api/main.py` 中添加 API 端点
4. 编写测试用例

### 代码规范
- 使用 Python 类型注解
- 遵循 PEP 8 代码风格
- 编写详细的文档字符串
- 使用异步编程模式

## 📝 更新日志

### v1.0.0 (2025-08-14)
- ✅ 初始版本发布
- ✅ 实现主题 CRUD 功能
- ✅ 实现文档关联功能
- ✅ 集成 Manticore Search
- ✅ 添加健康检查和监控
- ✅ 完成单元测试和集成测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
